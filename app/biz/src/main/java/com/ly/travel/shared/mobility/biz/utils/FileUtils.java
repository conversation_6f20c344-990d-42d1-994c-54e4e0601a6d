package com.ly.travel.shared.mobility.biz.utils;

import com.github.junrar.Archive;
import com.github.junrar.exception.UnsupportedRarV5Exception;
import com.github.junrar.rarfile.FileHeader;
import com.ly.travel.shared.mobility.biz.model.vo.meta.FileMetaInfo;
import com.ly.travel.shared.mobility.biz.model.vo.meta.UploadFileInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipFile;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Description: 文件处理
 * @Author: jay.he
 * @Date: 2025-08-18 15:49
 * @Version: 1.0
 **/
@Slf4j
public class FileUtils {

    /**
     * 解压文件并获取音视频文件的元数据信息
     *
     * @param zipFilePath 压缩文件路径
     * @return 音视频文件元数据列表
     */
    public static UploadFileInfoVO extractAndListMediaFiles(String filePath) {
        List<FileMetaInfo> fileList = new ArrayList<>();
//        // 创建解压目录
//        String extractDir = zipFilePath.substring(0, zipFilePath.lastIndexOf(".")) + "_extracted/";
//        new File(extractDir).mkdirs();
//
//        try {
//            if (zipFilePath.toLowerCase().endsWith(".zip")) {
//                // 解压ZIP文件
//                unzipMediaFiles(zipFilePath, extractDir);
//            } else if (zipFilePath.toLowerCase().endsWith(".rar")) {
//                // 解压RAR文件
//                unrarMediaFiles(zipFilePath, extractDir);
//            }
//
//            // 遍历解压后的目录，获取所有音视频文件（包括嵌套目录中的文件）
//            scanMediaFilesInDirectory(new File(extractDir), fileList);
//        } catch (Exception e) {
//            log.error("Error extracting and listing media files: {}", e);
//        }
        boolean needDel = true;
        try {
            if (filePath.toLowerCase().endsWith(".zip")) {
                // 创建解压目录
                String extractDir = filePath.substring(0, filePath.lastIndexOf(".")) + "_extracted/";
                new File(extractDir).mkdirs();
                // 解压ZIP文件
                unzipMediaFiles(filePath, extractDir);
                // 遍历解压后的目录，获取所有音视频文件（包括嵌套目录中的文件）
                scanMediaFilesInDirectory(new File(extractDir), fileList);
            } else if (filePath.toLowerCase().endsWith(".rar")) {
                // 创建解压目录
                String extractDir = filePath.substring(0, filePath.lastIndexOf(".")) + "_extracted/";
                new File(extractDir).mkdirs();
                // 解压RAR文件
                unrarMediaFiles(filePath, extractDir);
                // 遍历解压后的目录，获取所有音视频文件（包括嵌套目录中的文件）
                scanMediaFilesInDirectory(new File(extractDir), fileList);
            } else {
                // 处理单个音视频文件
                String extension = getFileExtension(filePath).toLowerCase();
                Set<String> mediaExtensions = new HashSet<>(Arrays.asList(
                        "mp3", "wav", "flac", "aac", "m4a", "wma", "ogg",  // 音频格式
                        "mp4", "avi", "mov", "wmv", "flv", "mkv", "webm"   // 视频格式
                ));

                if (mediaExtensions.contains(extension)) {
                    File file = new File(filePath);
                    if (file.exists() && file.isFile()) {
                        try {
                            FileMetaInfo metadata = getFileMetadata(file);
                            fileList.add(metadata);
                        } catch (IOException e) {
                            log.error("Error getting metadata for file: {}", filePath, e);
                        }
                    }
                } else {
                    log.warn("Unsupported file type: {}", filePath);
                }
                needDel = false;
            }
        } catch (Exception e) {
            log.error("Error extracting and listing media files: {}", e);
            e.printStackTrace();
        }

        List<FileMetaInfo> fileMetaInfoList = fileList.stream()
                .filter(fileMetaInfo -> !fileMetaInfo.getFileName().startsWith("."))
                .collect(Collectors.toList());

        return UploadFileInfoVO.builder()
                .fileMetaInfoList(fileMetaInfoList)
                .needDel(needDel)
                .build();
    }

    /**
     * 解压ZIP文件
     */
    public static void unzipMediaFiles(String zipFilePath, String extractDir) throws IOException {
        try (ZipFile zipFile = new ZipFile(new File(zipFilePath))) {
            Enumeration<ZipArchiveEntry> entries = zipFile.getEntries();

            while (entries.hasMoreElements()) {
                ZipArchiveEntry entry = entries.nextElement();
                String entryName = entry.getName();
                File destFile = new File(extractDir, entryName);

                if (entry.isDirectory()) {
                    destFile.mkdirs();
                } else {
                    // 确保父目录存在
                    destFile.getParentFile().mkdirs();

                    // 解压文件
                    try (InputStream is = zipFile.getInputStream(entry);
                         FileOutputStream fos = new FileOutputStream(destFile)) {
                        byte[] buffer = new byte[1024];
                        int length;
                        while ((length = is.read(buffer)) > 0) {
                            fos.write(buffer, 0, length);
                        }
                    }

                    // 设置文件的修改时间为压缩包中的时间
                    destFile.setLastModified(entry.getTime());
                }
            }
        }
    }

    /**
     * 解压RAR文件
     * 支持RAR v4及以下版本（使用junrar库）和RAR v5版本（使用外部unrar命令）
     */
    public static void unrarMediaFiles(String rarFilePath, String extractDir) throws Exception {
        try {
            // 首先尝试使用junrar库解压（适用于RAR v4及以下版本）
            unrarWithJunrar(rarFilePath, extractDir);
            log.info("Successfully extracted RAR file using junrar library: {}", rarFilePath);
        } catch (UnsupportedRarV5Exception e) {
            // 如果是RAR v5格式，使用外部unrar命令
            log.warn("RAR v5 format detected, falling back to external unrar command: {}", rarFilePath);
            unrarWithExternalCommand(rarFilePath, extractDir);
            log.info("Successfully extracted RAR v5 file using external unrar command: {}", rarFilePath);
        } catch (Exception e) {
            // 如果junrar失败且不是RAR v5异常，也尝试外部命令作为备选方案
            log.warn("junrar extraction failed, trying external unrar command as fallback: {}", e.getMessage());
            try {
                unrarWithExternalCommand(rarFilePath, extractDir);
                log.info("Successfully extracted RAR file using external unrar command as fallback: {}", rarFilePath);
            } catch (Exception fallbackException) {
                log.error("Both junrar and external unrar command failed for file: {}", rarFilePath);
                throw new Exception("Failed to extract RAR file: " + rarFilePath +
                    ". junrar error: " + e.getMessage() +
                    ", external unrar error: " + fallbackException.getMessage(), e);
            }
        }
    }

    /**
     * 使用junrar库解压RAR文件（适用于RAR v4及以下版本）
     */
    private static void unrarWithJunrar(String rarFilePath, String extractDir) throws Exception {
        try (Archive archive = new Archive(new File(rarFilePath))) {
            FileHeader fileHeader;
            while ((fileHeader = archive.nextFileHeader()) != null) {
                String fileName = fileHeader.getFileNameString().trim();
                File destFile = new File(extractDir, fileName);

                if (fileHeader.isDirectory()) {
                    destFile.mkdirs();
                } else {
                    // 确保父目录存在
                    destFile.getParentFile().mkdirs();

                    // 解压文件
                    try (FileOutputStream fos = new FileOutputStream(destFile)) {
                        archive.extractFile(fileHeader, fos);
                    }

                    // 设置文件的修改时间
                    if (fileHeader.getMTime() != null) {
                        destFile.setLastModified(fileHeader.getMTime().getTime());
                    }
                }
            }
        }
    }

    /**
     * 使用外部unrar命令解压RAR文件（适用于RAR v5版本）
     */
    private static void unrarWithExternalCommand(String rarFilePath, String extractDir) throws Exception {
        // 检查unrar命令是否可用
        if (!isUnrarCommandAvailable()) {
            throw new Exception("External unrar command is not available. Please install unrar tool.");
        }

        // 确保解压目录存在
        File extractDirFile = new File(extractDir);
        if (!extractDirFile.exists()) {
            extractDirFile.mkdirs();
        }

        // 构建unrar命令
        List<String> command = new ArrayList<>();
        command.add("unrar");
        command.add("x");  // 解压并保持目录结构
        command.add("-o+"); // 覆盖已存在的文件
        command.add("-y");  // 对所有询问回答yes
        command.add(rarFilePath);
        command.add(extractDir);

        // 执行命令
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.redirectErrorStream(true);

        Process process = processBuilder.start();

        // 读取命令输出
        StringBuilder output = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }
        }

        // 等待命令执行完成
        boolean finished = process.waitFor(60, TimeUnit.SECONDS); // 60秒超时
        if (!finished) {
            process.destroyForcibly();
            throw new Exception("unrar command timed out after 60 seconds");
        }

        int exitCode = process.exitValue();
        if (exitCode != 0) {
            throw new Exception("unrar command failed with exit code " + exitCode + ". Output: " + output.toString());
        }

        log.debug("unrar command output: {}", output.toString());
    }

    /**
     * 检查系统是否安装了unrar命令
     */
    private static boolean isUnrarCommandAvailable() {
        try {
            ProcessBuilder processBuilder = new ProcessBuilder("unrar");
            Process process = processBuilder.start();
            boolean finished = process.waitFor(5, TimeUnit.SECONDS);
            if (!finished) {
                process.destroyForcibly();
            }
            return true;
        } catch (Exception e) {
            log.debug("unrar command not available: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 扫描目录中的媒体文件并获取元信息
     */
    public static void scanMediaFilesInDirectory(File directory, List<FileMetaInfo> fileList) {
        // 定义音视频文件扩展名
        Set<String> mediaExtensions = new HashSet<>(Arrays.asList(
                "mp3", "wav", "flac", "aac", "m4a", "wma", "ogg",  // 音频格式
                "mp4", "avi", "mov", "wmv", "flv", "mkv", "webm"   // 视频格式
        ));

        scanDirectory(directory, mediaExtensions, fileList);
    }

    /**
     * 递归扫描目录
     */
    public static void scanDirectory(File directory, Set<String> mediaExtensions, List<FileMetaInfo> fileList) {
        File[] files = directory.listFiles();
        if (files == null) return;

        for (File file : files) {
            if (file.isDirectory()) {
                // 递归扫描子目录
                scanDirectory(file, mediaExtensions, fileList);
            } else {
                String fileName = file.getName();
                String extension = getFileExtension(fileName).toLowerCase();

                if (mediaExtensions.contains(extension)) {
                    try {
                        FileMetaInfo metadata = getFileMetadata(file);
                        fileList.add(metadata);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    /**
     * 获取文件元信息
     */
    public static FileMetaInfo getFileMetadata(File file) throws IOException {
        Path path = file.toPath();
        BasicFileAttributes attrs = Files.readAttributes(path, BasicFileAttributes.class);

        return FileMetaInfo.builder()
                .fileName(file.getName())
                .filePath(file.getAbsolutePath())
                .fileSize(attrs.size())
                .fileSizeStr(formatFileSize(attrs.size()))
                .createTime(new Date(attrs.creationTime().toMillis()))
                .modifyTime(new Date(attrs.lastModifiedTime().toMillis()))
                .extension(getFileExtension(file.getName()))
                .isReadable(file.canRead())
                .build();
    }

    /**
     * 格式化文件大小
     */
    public static String formatFileSize(long size) {
        if (size <= 0) return "0 B";

        final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));

        return String.format("%.2f %s", size / Math.pow(1024, digitGroups), units[digitGroups]);
    }

    /**
     * 获取文件扩展名
     */
    public static String getFileExtension(String fileName) {
        int lastIndexOf = fileName.lastIndexOf(".");
        if (lastIndexOf == -1) {
            return "";
        }
        return fileName.substring(lastIndexOf + 1);
    }

    /**
     * 异步删除文件
     *
     * @param filePath 文件路径
     * @return CompletableFuture
     */
    public static CompletableFuture<Boolean> delFileAsync(String filePath) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                delFile(filePath);
                return true;
            } catch (Exception e) {
                log.error("异步删除文件失败: {}", filePath, e);
                return false;
            }
        });
    }

    /**
     * 删除文件
     *
     * @param filePath 文件路径
     * @return
     */
    public static boolean delFile(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            throw new IllegalArgumentException("文件路径不能为空");
        }

        try {
            Path path = Paths.get(filePath);
            Files.deleteIfExists(path); // 如果文件存在则删除
            log.info("文件删除成功: {}", filePath);
            return true;
        } catch (IOException e) {
            log.error("删除文件失败: {}", filePath, e);
            throw new RuntimeException("删除文件失败", e);
        }
    }
}
