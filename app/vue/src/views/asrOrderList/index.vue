<template>
  <div v-cloak class="order-list-container">
    <div class="page-header">
      <h2 class="page-title">
        <i class="el-icon-document"></i>
        订单列表
      </h2>
    </div>

    <el-form :model="searchForm" class="demo-form-inline" size="mini" label-width="100px">
      <!-- 第一行：4个查询项 -->
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="订单ID">
            <el-input v-model="searchForm.orderSerialNo" clearable placeholder="请输入订单ID"
                      maxlength="100"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="车牌号">
            <el-input v-model="searchForm.plateNo" clearable placeholder="请输入车牌号">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="上传状态">
            <el-select v-model="searchForm.uploadStatus" placeholder="请选择" clearable style="width: 100%">
              <el-option label="待上传" :value="0"/>
              <el-option label="已上传" :value="1"/>
              <el-option label="不合格" :value="2"/>
              <el-option label="合格" :value="3"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="审核状态">
            <el-select v-model="searchForm.auditStatus" placeholder="请选择" clearable style="width: 100%">
              <el-option label="待审核" :value="0"/>
              <el-option label="已审核" :value="1"/>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第二行：产品类型和行程时间 -->
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="产品类型">
            <el-select v-model="searchForm.orderType" placeholder="请选择" clearable style="width: 100%">
              <el-option
                v-for="item in orderTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="行程时间">
            <el-row type="flex" align="middle" style="width: 100%">
              <el-date-picker
                  v-model="searchForm.tripStartTime"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="开始时间"
                  style="flex: 1">
              </el-date-picker>
              <span style="margin: 0 8px">至</span>
              <el-date-picker
                  v-model="searchForm.tripEndTime"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  default-time="23:59:59"
                  placeholder="结束时间"
                  style="flex: 1">
              </el-date-picker>
            </el-row>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item>
            <el-button size="mini" plain icon="el-icon-refresh" @click="resetClick">重置</el-button>
            <el-button size="mini" type="primary" plain icon="el-icon-search" @click="queryClick" style="margin-left: 10px">查询</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>


    <tablePage ref="multipleTable" :request-param="{
                url: 'inspector/queryAsrOrderInfoPage',
                method: 'POST',
                data: this.searchForm
            }">
      <el-table-column prop="orderSerialNo" align="center" label="订单ID"  width="150" />
      <el-table-column align="center" label="产品类型" width="120">
        <template slot-scope="{row}">
          {{ getOrderTypeLabel(row.orderType) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="行程时间" width="200">
        <template slot-scope="{row}">
          开始：{{ formatDate(row.tripStartTime) }}<br/>
          结束：{{ formatDate(row.tripEndTime) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="上传时间" width="150" >
        <template slot-scope="{row}">
          {{ formatDate(row.uploadTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="plateNo" align="center" label="车牌号">
      </el-table-column>
      <el-table-column prop="uploadStatusDesc" align="center" label="上传状态" width="120">
      </el-table-column>
      <el-table-column prop="auditStatusDesc" align="center" label="审核状态" width="120">
      </el-table-column>
      <el-table-column align="center" label="司机评分" width="160">
        <template slot-scope="{row}">
          <div v-if="row.ratingScore" class="rating-container">
            <el-rate
              :value="row.ratingScore"
              disabled
              :show-text="false"
              :show-score="false"
              text-color="#ff9900"
              void-color="#e4e7ed"
              disabled-void-color="#e4e7ed"
              :icon-classes="['el-icon-star-on', 'el-icon-star-on','el-icon-star-on']"
              class="compact-rate">
            </el-rate>
            <span class="score-text">{{ row.ratingScore }}</span>
          </div>
          <span v-else>--</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="操作" width="300" fixed="right">
        <template slot-scope="{row}">
          <el-button v-if="row.auditStatus ===0" type="primary" size="mini" plain icon="el-icon-upload"
                     @click="uploadClick(row)">
            {{ row.fileUrl ? '修改检查单' : '填写检查单' }}
          </el-button>
        </template>
      </el-table-column>
    </tablePage>
    <uploadModal :edit-info="editInfo" :is-show="showUploadModal" v-if="showUploadModal"
               @callbackclose="closeUploadModal"></uploadModal>
  </div>
</template>


<script>
import uploadModal from './components/uploadModal'
import tablePage from '@/components/tablePage'
import { formatDate } from '@/utils/index'

const defaultStartTime = new Date();
defaultStartTime.setDate(defaultStartTime.getDate() - 90);

let orgSearchForm = {
  orderSerialNo: '',
  tripStartTime: formatDate(defaultStartTime,'yyyy-MM-dd 00:00:00'),
  tripEndTime: formatDate(new Date(),'yyyy-MM-dd 23:59:59'),
  plateNo: '',
  uploadStatus: '',
  auditStatus: '',
  orderType: ''
}


export default {
  components: {
    uploadModal,
    tablePage
  },
  data() {
    return {
      searchForm: this.deepCopy(orgSearchForm),
      editInfo: {},
      showUploadModal: false,
      orderTypeOptions: []
    }
  },

  created() {
    this.loadOrderTypeOptions()
  },
  mounted() {
    this.queryClick()
  },
  filters: {},
  methods: {
    loadOrderTypeOptions() {
      this.$http({
        url: 'inspector/orderTypeOptions',
        method: 'POST',
        data: {}
      }).then((res) => {
        if (res.success) {
          this.orderTypeOptions = res.data
        }
      }).catch(err => {
        console.log(err)
      })
    },
    uploadClick(row){
      this.editInfo = this.deepCopy(row)
      this.showUploadModal = true
    },
    //重置
    resetClick() {
      this.searchForm = this.deepCopy(orgSearchForm)
    },
    //查询
    queryClick() {
      this.$refs.multipleTable.searchData(1)
    },
    closeUploadModal(result) {
      this.showUploadModal = false
      if (result) {
        this.$refs.multipleTable.searchData()
      }
    },
    getOrderTypeLabel(orderType) {
      if (!orderType) return '--'
      const option = this.orderTypeOptions.find(item => item.value === orderType)
      return option ? option.label : '未知类型'
    }
  }
};
</script>

<style lang="scss" scoped>
.order-list-container {
  .page-header {
    margin-bottom: 20px;

    .page-title {
      font-size: 20px;
      color: #303133;
      margin: 0;
      display: flex;
      align-items: center;

      i {
        margin-right: 8px;
        color: #409EFF;
      }
    }
  }
  .demo-form-inline {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;

    .el-form-item {
      margin-bottom: 16px;
    }

    .el-row {
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .el-button {
      margin: 0 5px;
    }
  }

  // 表格样式优化
  ::v-deep .el-table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .el-table__header {
      background-color: #f8f9fa;

      th {
        background-color: #f8f9fa !important;
        color: #303133;
        font-weight: 600;
      }
    }

    .el-table__row {
      &:hover {
        background-color: #f5f7fa;
      }
    }
  }

  // 分页样式
  ::v-deep .el-pagination {
    text-align: center;
    margin-top: 20px;
    padding: 20px 0;
  }

  // 评分组件样式优化
  .rating-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;

    .compact-rate {
      ::v-deep .el-rate__item {
        margin-right: 2px !important;

        .el-rate__icon {
          font-size: 14px !important;
        }
      }
    }

    .score-text {
      font-size: 12px;
      color: #ff9900;
      font-weight: 500;
      min-width: 20px;
    }
  }
}
</style>
